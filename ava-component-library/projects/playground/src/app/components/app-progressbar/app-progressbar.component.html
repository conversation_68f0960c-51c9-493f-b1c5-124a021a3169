<div class="demo-page">
  <!-- Demo Header -->
  <div class="demo-header">
    <div class="container">
      <h1>Progress Bar</h1>
      <p>
        A versatile progress bar component with circular and linear variants,
        supporting determinate, indeterminate, buffer, and query modes with
        customizable colors, animations, and accessibility features.
      </p>
    </div>
  </div>

  <!-- Demo Navigation -->
  <div class="demo-content">
    <div class="container">
      <div class="demo-grid">
        <div class="demo-card">
          <h3>Circular Progress</h3>
          <p>
            SVG-based circular indicator with smooth animations and customizable
            size, color, and position.
          </p>
          <a routerLink="/progressbar-circular" class="demo-link"
            >View Demo →</a
          >
        </div>

        <div class="demo-card">
          <h3>Linear Progress</h3>
          <p>
            Horizontal progress bar with support for determinate, indeterminate,
            and buffer modes.
          </p>
          <a routerLink="/progressbar-linear" class="demo-link">View Demo →</a>
        </div>

        <div class="demo-card">
          <h3>Progress Modes</h3>
          <p>
            Explore different progress modes: determinate, indeterminate,
            buffer, and query states.
          </p>
          <a routerLink="/progressbar-modes" class="demo-link">View Demo →</a>
        </div>

        <div class="demo-card">
          <h3>Customization</h3>
          <p>
            Custom colors, sizes, positions, and advanced styling options for
            progress bars.
          </p>
          <a routerLink="/progressbar-customization" class="demo-link"
            >View Demo →</a
          >
        </div>

        <div class="demo-card">
          <h3>Use Cases</h3>
          <p>
            Real-world examples: file uploads, data processing, loading states,
            and video buffering.
          </p>
          <a routerLink="/progressbar-use-cases" class="demo-link"
            >View Demo →</a
          >
        </div>

        <div class="demo-card">
          <h3>API Reference</h3>
          <p>
            Complete API documentation with all inputs, outputs, properties, and
            methods.
          </p>
          <a routerLink="/progressbar-api" class="demo-link">View Demo →</a>
        </div>
      </div>
    </div>
  </div>
</div>
