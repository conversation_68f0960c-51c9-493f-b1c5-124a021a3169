// =============================================================================
// DATA GRID BASIC USAGE DEMO - CLEAN & REFACTORED STYLES
// =============================================================================

// =============================================================================
// LAYOUT & STRUCTURE
// =============================================================================

.demo-page {
  min-height: 100vh;
  background: var(--color-background-primary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.demo-content {
  width: 100%;
  max-width: 1000px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.demo-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--global-spacing-10, 5rem);

  &:last-child {
    margin-bottom: 0;
  }
}

// =============================================================================
// TABLE CONTAINER
// =============================================================================

.table-container {
  width: 100%;
  max-width: 800px;
  display: flex;
  justify-content: center;
}

// =============================================================================
// TABLE STYLING
// =============================================================================

.styled-data-grid {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
