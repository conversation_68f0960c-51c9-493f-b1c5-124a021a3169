:root {
    --grid-font-family-body: var(--font-family-body);
    --grid-header-background-color: var(--color-background-secondary);
    --grid-border: var(--color-border-subtle);
    --grid-background-color-odd: var(--color-background-primary);
    --grid-background-color-even: var(--color-background-disabled);
    --grid-text-disabled: var(--color-text-disabled);
    --grid-text-color: var(--color-text-primary);
    --grid-filter-active-color: var(--color-brand-primary);
    --grid-cell-paading: var(--global-spacing-4);

    /* Figma Typography Specifications */
    --grid-figma-font-family: 'Inter', sans-serif;
    --grid-figma-font-size: 14px;
    --grid-figma-font-weight: 400;
    --grid-figma-line-height-regular: 20px; /* 142.857% */
    --grid-figma-line-height-140: 19.6px; /* 140% */
    --grid-figma-text-color: #0F2028;

    /* Column-specific styling */
    --grid-journal-description-gap: var(--global-spacing-3);
    --grid-dr-cr-gap: var(--global-spacing-3);
}